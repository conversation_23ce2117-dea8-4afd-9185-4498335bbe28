"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __exportStar = (this && this.__exportStar) || function(m, exports) {
    for (var p in m) if (p !== "default" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.uuid = exports.measureTextWidth = exports.measureTextHeight = void 0;
__exportStar(require("./rc"), exports);
__exportStar(require("./react"), exports);
var measure_text_1 = require("./utils/measure-text");
Object.defineProperty(exports, "measureTextHeight", { enumerable: true, get: function () { return measure_text_1.measureTextHeight; } });
Object.defineProperty(exports, "measureTextWidth", { enumerable: true, get: function () { return measure_text_1.measureTextWidth; } });
var uuid_1 = require("./uuid");
Object.defineProperty(exports, "uuid", { enumerable: true, get: function () { return uuid_1.uuid; } });
